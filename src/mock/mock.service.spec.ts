import "module-alias/register";
import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { Test, TestingModule } from "@nestjs/testing";
import { MockService } from "./mock.service";
import { mock } from "@skywind-group/sw-integration-core";
import {
    OperatorAuthRequest,
    OperatorDebitRequest,
    OperatorCreditRequest,
    OperatorDebitCreditRequest,
    OperatorRollbackRequest,
    OperatorPromoRequest,
    OperatorPromoType,
    operatorErrorCodes
} from "@entities/operator.entities";

@suite
class MockServiceTest {
    private service: MockService;
    private settingsService: mock.SettingsService;
    private ticketService: any;

    async before() {
        this.ticketService = {
            getDataFromTicket: (token: string) => ["2019045569026", "USD"]
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                MockService,
                {
                    provide: mock.SettingsService,
                    useValue: {
                        settings: { amount: 100 }
                    }
                },
                {
                    provide: mock.TicketService,
                    useValue: this.ticketService
                }
            ]
        }).compile();

        this.service = module.get<MockService>(MockService);
        this.settingsService = module.get<mock.SettingsService>(mock.SettingsService);
    }

    @test
    "should be defined"() {
        expect(this.service).to.exist;
    }

    @test
    async "should return customer balance and trader ID for auth"() {
        const request: OperatorAuthRequest = {
            customer: "2019045569026",
            token: "valid_token"
        };

        const response = await this.service.auth(request);

        expect(response).to.deep.equal({
            balance: 1000,
            bonusBalance: 50,
            code: 0,
            currency: "USD",
            status: "SUCCESS",
            traderId: 1
        });
    }

    @test
    async "should return error for suspended customer"() {
        this.ticketService.getDataFromTicket = () => ["2021030100001", "USD"];

        const request: OperatorAuthRequest = {
            customer: "2021030100001",
            token: "valid_token"
        };

        const response = await this.service.auth(request);

        expect(response.code).to.equal(operatorErrorCodes.PLAYER_SUSPENDED);
        expect(response.status).to.equal("PLAYER_SUSPENDED");
    }

    @test
    async "should debit customer balance successfully"() {
        // Reset to ensure clean state
        this.ticketService.getDataFromTicket = () => ["2019045569026", "USD"];

        const request: OperatorDebitRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456"
        };

        const response = await this.service.debit(request);

        expect(response.code).to.equal(0);
        expect(response.status).to.equal("SUCCESS");
        expect(response.balance).to.equal(1000); // Balance unchanged, bonus used first
        expect(response.bonusBalance).to.equal(40); // 50 - 10
    }

    @test
    async "should return insufficient funds error"() {
        this.ticketService.getDataFromTicket = () => ["2019045569026", "USD"];

        const request: OperatorDebitRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 2000, // More than available balance
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456"
        };

        const response = await this.service.debit(request);

        expect(response.code).to.equal(operatorErrorCodes.INSUFFICIENT_FUNDS);
        expect(response.status).to.equal("INSUFFICIENT_FUNDS");
    }

    @test
    async "should credit customer balance after debit"() {
        this.getDataFromTicketStub.returns(["2019045569026", "USD"]);

        // First place a bet
        const debitRequest: OperatorDebitRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456"
        };

        await this.service.debit(debitRequest);

        // Then settle the win
        const creditRequest: OperatorCreditRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 25,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_789"
        };

        const response = await this.service.credit(creditRequest);

        expect(response.code).to.equal(0);
        expect(response.status).to.equal("SUCCESS");
    }

    @test
    async "should return error for non-existent round"() {
        this.getDataFromTicketStub.returns(["2019045569026", "USD"]);

        const request: OperatorCreditRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 25,
            currency: "USD",
            betId: "non_existent_bet",
            trxId: "trx_789"
        };

        const response = await this.service.credit(request);

        expect(response.code).to.equal(operatorErrorCodes.BET_RECORD_NOT_FOUND);
        expect(response.status).to.equal("BET_RECORD_NOT_FOUND");
    }

    @test
    async "should process debit and credit in one transaction"() {
        this.getDataFromTicketStub.returns(["2019045569026", "USD"]);

        const request: OperatorDebitCreditRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 10,
            creditAmount: 25,
            currency: "USD",
            betId: "bet_123",
            trxId: "trx_456",
            creditTrxId: "trx_789"
        };

        const response = await this.service.debitCredit(request);

        expect(response.code).to.equal(0);
        expect(response.status).to.equal("SUCCESS");
        expect(response.trxId).to.exist;
        expect(response.creditTrxId).to.exist;
    }

    @test
    async "should process freespin promotional win"() {
        this.getDataFromTicketStub.returns(["2019045569026", "USD"]);

        const request: OperatorPromoRequest = {
            customer: "2019045569026",
            token: "valid_token",
            gameId: "test_game",
            amount: 15,
            currency: "USD",
            betId: "promo_bet_123",
            trxId: "promo_trx_456",
            promo: {
                promoType: OperatorPromoType.FSW,
                promoRef: "freespin_ref_123"
            }
        };

        const response = await this.service.promo(request);

        expect(response.code).to.equal(0);
        expect(response.status).to.equal("SUCCESS");
        expect(response.bonusBalance).to.equal(65); // 50 + 15 freespin win
    }

    @test
    "should retrieve customer data"() {
        const customer = this.service.getCustomerData("2019045569026");
        expect(customer).to.exist;
        expect(customer?.customerId).to.equal("2019045569026");
        expect(customer?.balance).to.equal(1000);
    }

    @test
    "should reset mock data"() {
        this.service.resetMockData();
        const customer = this.service.getCustomerData("2019045569026");
        expect(customer?.balance).to.equal(1000); // Should be reset to initial value
    }
}
