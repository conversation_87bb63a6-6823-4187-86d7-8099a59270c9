import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MockController } from "./mock.controller";
import { MockService } from "./mock.service";
import { AuthResponder } from "./responders/auth.responder";
import { DebitCreditResponder } from "./responders/debit-credit.responder";
import { PaymentResponder } from "./responders/payment.responder";

@Module({
    controllers: [MockController],
    providers: [
        MockService,
        AuthResponder,
        PaymentResponder,
        DebitCreditResponder
    ],
    imports: [mock.MockModule]
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        consumer
            .apply(AuthResponder).forRoutes("/auth")
            .apply(PaymentResponder).forRoutes("/debit")
            .apply(PaymentResponder).forRoutes("/credit")
            .apply(DebitCreditResponder).forRoutes("/debit-credit")
            .apply(PaymentResponder).forRoutes("/rollback")
            .apply(PaymentResponder).forRoutes("/promo");
    }
}
