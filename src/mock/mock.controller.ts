import {
    OperatorAuthRequest,
    OperatorAuthResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorPromoRequest,
    OperatorPromoResponse,
    OperatorRollbackRequest,
    OperatorRollbackResponse
} from "@entities/operator.entities";
import { MockService } from "./mock.service";
import { Body, Controller, HttpCode, Post, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Controller("/casino-engine/generic/")
@UseInterceptors(mock.CustomErrorInterceptor, mock.ExtraDataInterceptor)
export class MockController {

    constructor(private readonly mockService: MockService) {}

    @Post("/:vendorCode/v5/auth")
    @HttpCode(200)
    public async auth(@Body() body: OperatorAuthRequest): Promise<OperatorAuthResponse> {
        return this.mockService.auth(body);
    }

    @Post("/:vendorCode/v5/debit")
    @HttpCode(200)
    public async debit(@Body() body: OperatorDebitRequest): Promise<OperatorDebitResponse> {
        return this.mockService.debit(body);
    }

    @Post("/:vendorCode/v5/credit")
    @HttpCode(200)
    public async credit(@Body() body: OperatorCreditRequest): Promise<OperatorCreditResponse> {
        return this.mockService.credit(body);
    }

    @Post("/:vendorCode/v5/debit-credit")
    @HttpCode(200)
    public async debitCredit(@Body() body: OperatorDebitCreditRequest): Promise<OperatorDebitCreditResponse> {
        return this.mockService.debitCredit(body);
    }

    @Post("/:vendorCode/v5/rollback")
    @HttpCode(200)
    public async rollback(@Body() body: OperatorRollbackRequest): Promise<OperatorRollbackResponse> {
        return this.mockService.rollback(body);
    }

    @Post("/:vendorCode/v5/promo")
    @HttpCode(200)
    public async promo(@Body() body: OperatorPromoRequest): Promise<OperatorPromoResponse> {
        return this.mockService.promo(body);
    }
}
