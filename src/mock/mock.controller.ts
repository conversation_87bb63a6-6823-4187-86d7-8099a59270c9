import {
    OperatorAuthRequest,
    OperatorAuthResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorPromoRequest,
    OperatorPromoResponse,
    OperatorRollbackRequest,
    OperatorRollbackResponse
} from "@entities/operator.entities";
import { getOperatorPaymentResponse, getOperatorResponse } from "@mock/utils/response-builder";
import { Body, Controller, HttpCode, Post, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Controller("/casino-engine/generic/")
@UseInterceptors(mock.CustomErrorInterceptor, mock.ExtraDataInterceptor)
export class MockController {

    @Post("/:vendorCode/v5/auth")
    @HttpCode(200)
    public async auth(@Body() body: OperatorAuthRequest): Promise<OperatorAuthResponse> {
        return getOperatorResponse(0, "");
    }

    @Post("/:vendorCode/v5/debit")
    @HttpCode(200)
    public async debit(@Body() body: OperatorDebitRequest): Promise<OperatorDebitResponse> {
        return getOperatorPaymentResponse(0, body.currency);
    }

    @Post("/:vendorCode/v5/credit")
    @HttpCode(200)
    public async credit(@Body() body: OperatorCreditRequest): Promise<OperatorCreditResponse> {
        return getOperatorPaymentResponse(0, body.currency);
    }

    @Post("/:vendorCode/v5/debit-credit")
    @HttpCode(200)
    public async debitCredit(@Body() body: OperatorDebitCreditRequest): Promise<OperatorDebitCreditResponse> {
        return getOperatorPaymentResponse(0, body.currency);
    }

    @Post("/:vendorCode/v5/rollback")
    @HttpCode(200)
    public async rollback(@Body() body: OperatorRollbackRequest): Promise<OperatorRollbackResponse> {
        return getOperatorPaymentResponse(0, "");
    }

    @Post("/:vendorCode/v5/promo")
    @HttpCode(200)
    public async promo(@Body() body: OperatorPromoRequest): Promise<OperatorPromoResponse> {
        return getOperatorPaymentResponse(0, body.currency);
    }
}
