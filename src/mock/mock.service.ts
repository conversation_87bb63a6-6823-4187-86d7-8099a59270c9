import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import {
    OperatorAuthRequest,
    OperatorAuthResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorRollbackRequest,
    OperatorRollbackResponse,
    OperatorPromoRequest,
    OperatorPromoResponse,
    operatorErrorCodes,
    operatorStatusMessages
} from "@entities/operator.entities";

interface MockCustomer {
    customerId: string;
    balance: number;
    bonusBalance: number;
    currency: string;
    suspended: boolean;
    country: string;
}

interface MockTransaction {
    trxId: string;
    externalTrxId: string;
    customerId: string;
    gameId: string;
    betId: string;
    amount: number;
    type: "debit" | "credit" | "rollback";
    status: "pending" | "settled" | "cancelled";
    timestamp: Date;
    currency: string;
}

interface MockRound {
    betId: string;
    customerId: string;
    gameId: string;
    debitAmount: number;
    creditAmount: number;
    status: "open" | "settled" | "cancelled";
    transactions: MockTransaction[];
    timestamp: Date;
}

interface MockSession {
    token: string;
    customerId: string;
    currency: string;
    createdAt: Date;
    expiresAt: Date;
    active: boolean;
}

@Injectable()
export class MockService {
    private customers = new Map<string, MockCustomer>();
    private transactions = new Map<string, MockTransaction>();
    private rounds = new Map<string, MockRound>();
    private sessions = new Map<string, MockSession>();
    private transactionCounter = 1;

    constructor(
        protected readonly settingsService: mock.SettingsService,
        protected readonly ticketService: mock.TicketService
    ) {
        this.initializeMockData();
    }

    private initializeMockData(): void {
        // Initialize default customers
        const defaultCustomers = [
            {
                customerId: "2019045569026",
                balance: 1000.00,
                bonusBalance: 50.00,
                currency: "USD",
                suspended: false,
                country: "US"
            },
            {
                customerId: "2020120400005",
                balance: 500.00,
                bonusBalance: 25.00,
                currency: "EUR",
                suspended: false,
                country: "DE"
            },
            {
                customerId: "2021030100001",
                balance: 0.00,
                bonusBalance: 0.00,
                currency: "USD",
                suspended: true,
                country: "UK"
            }
        ];

        defaultCustomers.forEach(customer => {
            this.customers.set(customer.customerId, customer);
        });
    }

    private generateTransactionId(): number {
        return this.transactionCounter++;
    }

    private validateToken(token: string): [string, string] {
        try {
            const result = this.ticketService.getDataFromTicket(token);
            // Handle both 2-element and 3-element returns
            if (Array.isArray(result) && result.length >= 2) {
                return [result[0], result[1]];
            }
            throw new Error("Invalid token data format");
        } catch (error) {
            throw this.createErrorResponse(operatorErrorCodes.TOKEN_INVALID, "TOKEN_INVALID");
        }
    }

    private getCustomer(customerId: string): MockCustomer {
        const customer = this.customers.get(customerId);
        if (!customer) {
            throw this.createErrorResponse(operatorErrorCodes.CUSTOMER_NOT_FOUND, "CUSTOMER_NOT_FOUND");
        }
        return customer;
    }

    private createErrorResponse(code: number, status: string): any {
        const error = new Error(operatorStatusMessages[code] || status);
        (error as any).code = code;
        (error as any).status = status;
        return error;
    }

    private createSuccessResponse(customer: MockCustomer, trxId?: number): any {
        const response = {
            balance: customer.balance,
            bonusBalance: customer.bonusBalance,
            code: operatorErrorCodes.SUCCESS,
            currency: customer.currency,
            status: "SUCCESS"
        };

        if (trxId !== undefined) {
            (response as any).trxId = trxId;
        }

        return response;
    }

    public async auth(request: OperatorAuthRequest): Promise<OperatorAuthResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            return {
                ...this.createSuccessResponse(customer),
                traderId: 1
            };
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    traderId: 0
                };
            }
            throw error;
        }
    }

    public async debit(request: OperatorDebitRequest): Promise<OperatorDebitResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            // Check if transaction already exists
            if (this.transactions.has(request.trxId)) {
                const existingTrx = this.transactions.get(request.trxId);
                if (existingTrx?.status === "settled") {
                    throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
                }
            }

            // Check sufficient funds
            const totalBalance = customer.balance + customer.bonusBalance;
            if (totalBalance < request.amount) {
                throw this.createErrorResponse(operatorErrorCodes.INSUFFICIENT_FUNDS, "INSUFFICIENT_FUNDS");
            }

            // Process debit
            const trxId = this.generateTransactionId();
            this.processDebit(customer, request.amount);

            // Create transaction record
            const transaction: MockTransaction = {
                trxId: trxId.toString(),
                externalTrxId: request.trxId,
                customerId,
                gameId: request.gameId,
                betId: request.betId,
                amount: request.amount,
                type: "debit",
                status: "pending",
                timestamp: new Date(),
                currency: request.currency
            };

            this.transactions.set(request.trxId, transaction);

            // Create or update round
            this.createOrUpdateRound(request.betId, customerId, request.gameId, request.amount, 0, transaction);

            return this.createSuccessResponse(customer, trxId);
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    trxId: 0
                };
            }
            throw error;
        }
    }

    private processDebit(customer: MockCustomer, amount: number): void {
        if (customer.bonusBalance >= amount) {
            customer.bonusBalance -= amount;
        } else {
            const remainingAmount = amount - customer.bonusBalance;
            customer.bonusBalance = 0;
            customer.balance -= remainingAmount;
        }
    }

    private processCredit(customer: MockCustomer, amount: number, debitAmount: number = 0): void {
        if (debitAmount > 0) {
            // For debit-credit, calculate proportional distribution
            const bonusRatio = debitAmount > 0 ? Math.min(customer.bonusBalance / debitAmount, 1) : 0;
            const bonusWin = amount * bonusRatio;
            const regularWin = amount - bonusWin;

            customer.bonusBalance += bonusWin;
            customer.balance += regularWin;
        } else {
            // For regular credit, add to balance
            customer.balance += amount;
        }
    }

    private createOrUpdateRound(betId: string, customerId: string, gameId: string, debitAmount: number, creditAmount: number, transaction: MockTransaction): void {
        let round = this.rounds.get(betId);

        if (!round) {
            round = {
                betId,
                customerId,
                gameId,
                debitAmount,
                creditAmount,
                status: "open",
                transactions: [],
                timestamp: new Date()
            };
            this.rounds.set(betId, round);
        }

        round.transactions.push(transaction);

        if (transaction.type === "debit") {
            round.debitAmount += debitAmount;
        } else if (transaction.type === "credit") {
            round.creditAmount += creditAmount;
            round.status = "settled";
        }
    }

    public async credit(request: OperatorCreditRequest): Promise<OperatorCreditResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            // Check if transaction already exists
            if (this.transactions.has(request.trxId)) {
                const existingTrx = this.transactions.get(request.trxId);
                if (existingTrx?.status === "settled") {
                    throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
                }
            }

            // Check if round exists for this betId
            const round = this.rounds.get(request.betId);
            if (!round) {
                throw this.createErrorResponse(operatorErrorCodes.BET_RECORD_NOT_FOUND, "BET_RECORD_NOT_FOUND");
            }

            if (round.status === "settled") {
                throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
            }

            // Process credit
            const trxId = this.generateTransactionId();
            this.processCredit(customer, request.amount, round.debitAmount);

            // Create transaction record
            const transaction: MockTransaction = {
                trxId: trxId.toString(),
                externalTrxId: request.trxId,
                customerId,
                gameId: request.gameId,
                betId: request.betId,
                amount: request.amount,
                type: "credit",
                status: "settled",
                timestamp: new Date(),
                currency: request.currency
            };

            this.transactions.set(request.trxId, transaction);

            // Update round
            this.createOrUpdateRound(request.betId, customerId, request.gameId, 0, request.amount, transaction);

            return this.createSuccessResponse(customer, trxId);
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    trxId: 0
                };
            }
            throw error;
        }
    }

    public async debitCredit(request: OperatorDebitCreditRequest): Promise<OperatorDebitCreditResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            // Check if transaction already exists
            if (this.transactions.has(request.trxId)) {
                const existingTrx = this.transactions.get(request.trxId);
                if (existingTrx?.status === "settled") {
                    throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
                }
            }

            // Check sufficient funds for debit
            const totalBalance = customer.balance + customer.bonusBalance;
            if (totalBalance < request.amount) {
                throw this.createErrorResponse(operatorErrorCodes.INSUFFICIENT_FUNDS, "INSUFFICIENT_FUNDS");
            }

            // Process debit-credit transaction
            const debitTrxId = this.generateTransactionId();
            const creditTrxId = this.generateTransactionId();

            // Process debit
            this.processDebit(customer, request.amount);

            // Process credit
            this.processCredit(customer, request.creditAmount, request.amount);

            // Create debit transaction record
            const debitTransaction: MockTransaction = {
                trxId: debitTrxId.toString(),
                externalTrxId: request.trxId,
                customerId,
                gameId: request.gameId,
                betId: request.betId,
                amount: request.amount,
                type: "debit",
                status: "settled",
                timestamp: new Date(),
                currency: request.currency
            };

            // Create credit transaction record
            const creditTransaction: MockTransaction = {
                trxId: creditTrxId.toString(),
                externalTrxId: request.creditTrxId,
                customerId,
                gameId: request.gameId,
                betId: request.betId,
                amount: request.creditAmount,
                type: "credit",
                status: "settled",
                timestamp: new Date(),
                currency: request.currency
            };

            this.transactions.set(request.trxId, debitTransaction);
            this.transactions.set(request.creditTrxId, creditTransaction);

            // Create round with both transactions
            const round: MockRound = {
                betId: request.betId,
                customerId,
                gameId: request.gameId,
                debitAmount: request.amount,
                creditAmount: request.creditAmount,
                status: "settled",
                transactions: [debitTransaction, creditTransaction],
                timestamp: new Date()
            };

            this.rounds.set(request.betId, round);

            return {
                ...this.createSuccessResponse(customer, debitTrxId),
                creditTrxId
            };
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    trxId: 0,
                    creditTrxId: 0
                };
            }
            throw error;
        }
    }

    public async rollback(request: OperatorRollbackRequest): Promise<OperatorRollbackResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            // Find the original transaction to rollback
            const originalTransaction = this.transactions.get(request.trxId);
            if (!originalTransaction) {
                throw this.createErrorResponse(operatorErrorCodes.TRANSACTION_NOT_FOUND, "TRANSACTION_NOT_FOUND");
            }

            if (originalTransaction.status === "cancelled") {
                throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
            }

            // Process rollback based on original transaction type
            const rollbackTrxId = this.generateTransactionId();

            if (originalTransaction.type === "debit") {
                // Rollback debit: return money to customer
                this.processCredit(customer, originalTransaction.amount);
            } else if (originalTransaction.type === "credit") {
                // Rollback credit: remove money from customer
                this.processDebit(customer, originalTransaction.amount);
            }

            // Mark original transaction as cancelled
            originalTransaction.status = "cancelled";

            // Create rollback transaction record
            const rollbackTransaction: MockTransaction = {
                trxId: rollbackTrxId.toString(),
                externalTrxId: `rollback_${request.trxId}`,
                customerId,
                gameId: request.gameId,
                betId: originalTransaction.betId,
                amount: originalTransaction.amount,
                type: "rollback",
                status: "settled",
                timestamp: new Date(),
                currency: originalTransaction.currency
            };

            this.transactions.set(`rollback_${request.trxId}`, rollbackTransaction);

            // Update round status if exists
            const round = this.rounds.get(originalTransaction.betId);
            if (round) {
                round.status = "cancelled";
                round.transactions.push(rollbackTransaction);
            }

            return this.createSuccessResponse(customer, rollbackTrxId);
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    trxId: 0
                };
            }
            throw error;
        }
    }

    public async promo(request: OperatorPromoRequest): Promise<OperatorPromoResponse> {
        try {
            const [customerId] = this.validateToken(request.token);
            const customer = this.getCustomer(customerId);

            if (customer.suspended) {
                throw this.createErrorResponse(operatorErrorCodes.PLAYER_SUSPENDED, "PLAYER_SUSPENDED");
            }

            // Validate promo type
            const validPromoTypes = ["FSW", "JPW", "CB", "TW", "RW", "REW", "CDW", "RB"];
            if (!validPromoTypes.includes(request.promo.promoType)) {
                throw this.createErrorResponse(operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED, "PROMOTION_TYPE_NOT_SUPPORTED");
            }

            // Check if transaction already exists
            if (this.transactions.has(request.trxId)) {
                const existingTrx = this.transactions.get(request.trxId);
                if (existingTrx?.status === "settled") {
                    throw this.createErrorResponse(operatorErrorCodes.BET_ALREADY_SETTLED, "BET_ALREADY_SETTLED");
                }
            }

            // Process promotional win
            const trxId = this.generateTransactionId();

            // For promotional wins, typically add to bonus balance first
            if (request.promo.promoType === "FSW") {
                // Freespin wins go to bonus balance
                customer.bonusBalance += request.amount;
            } else {
                // Other promotional wins go to regular balance
                customer.balance += request.amount;
            }

            // Create transaction record
            const transaction: MockTransaction = {
                trxId: trxId.toString(),
                externalTrxId: request.trxId,
                customerId,
                gameId: request.gameId,
                betId: request.betId,
                amount: request.amount,
                type: "credit",
                status: "settled",
                timestamp: new Date(),
                currency: request.currency
            };

            this.transactions.set(request.trxId, transaction);

            // Create promotional round
            const promoRound: MockRound = {
                betId: request.betId,
                customerId,
                gameId: request.gameId,
                debitAmount: 0,
                creditAmount: request.amount,
                status: "settled",
                transactions: [transaction],
                timestamp: new Date()
            };

            this.rounds.set(request.betId, promoRound);

            return this.createSuccessResponse(customer, trxId);
        } catch (error) {
            if (error.code) {
                return {
                    balance: 0,
                    bonusBalance: 0,
                    code: error.code,
                    currency: "",
                    status: error.status,
                    trxId: 0
                };
            }
            throw error;
        }
    }

    // Utility methods for testing and data management
    public getCustomerData(customerId: string): MockCustomer | undefined {
        return this.customers.get(customerId);
    }

    public getTransactionData(trxId: string): MockTransaction | undefined {
        return this.transactions.get(trxId);
    }

    public getRoundData(betId: string): MockRound | undefined {
        return this.rounds.get(betId);
    }

    public addCustomer(customer: MockCustomer): void {
        this.customers.set(customer.customerId, customer);
    }

    public updateCustomerBalance(customerId: string, balance: number, bonusBalance: number): void {
        const customer = this.customers.get(customerId);
        if (customer) {
            customer.balance = balance;
            customer.bonusBalance = bonusBalance;
        }
    }

    public resetMockData(): void {
        this.customers.clear();
        this.transactions.clear();
        this.rounds.clear();
        this.sessions.clear();
        this.transactionCounter = 1;
        this.initializeMockData();
    }

    public getAllCustomers(): MockCustomer[] {
        return Array.from(this.customers.values());
    }

    public getAllTransactions(): MockTransaction[] {
        return Array.from(this.transactions.values());
    }

    public getAllRounds(): MockRound[] {
        return Array.from(this.rounds.values());
    }

    public getCustomerTransactions(customerId: string): MockTransaction[] {
        return Array.from(this.transactions.values()).filter(tx => tx.customerId === customerId);
    }

    public getCustomerRounds(customerId: string): MockRound[] {
        return Array.from(this.rounds.values()).filter(round => round.customerId === customerId);
    }
}
