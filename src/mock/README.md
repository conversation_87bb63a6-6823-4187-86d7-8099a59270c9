# MockService for Generic Vendor API v5

This MockService class extends the mock system from `@skywind-group/sw-integration-core` and implements all endpoints of the Generic Vendor API v5 specification.

## Overview

The MockService provides a complete implementation of the Generic Vendor API v5 with realistic business logic, including:

- Customer balance management (regular and bonus balances)
- Transaction tracking and validation
- Round management
- Error handling with proper status codes
- Token validation
- Promotional wins support

## Supported Endpoints

### `/auth`
- **Purpose**: Authentication and balance retrieval
- **Features**: 
  - Token validation
  - Customer lookup
  - Suspended player detection
  - Returns balance, bonus balance, and trader ID

### `/debit`
- **Purpose**: Place bets (debit customer balance)
- **Features**:
  - Sufficient funds validation
  - Duplicate transaction detection
  - Proportional deduction from bonus and regular balance
  - Round creation
  - Transaction tracking

### `/credit`
- **Purpose**: Settle wins (credit customer balance)
- **Features**:
  - Round validation
  - Duplicate transaction detection
  - Proportional credit distribution
  - Round settlement
  - Transaction tracking

### `/debit-credit`
- **Purpose**: Combined bet placement and settlement
- **Features**:
  - Atomic debit and credit operations
  - Proportional balance calculations
  - Complete round lifecycle
  - Dual transaction tracking

### `/rollback`
- **Purpose**: Reverse previous transactions
- **Features**:
  - Original transaction lookup
  - Balance restoration
  - Transaction cancellation
  - Round status updates

### `/promo`
- **Purpose**: Promotional wins (freespins, jackpots, etc.)
- **Features**:
  - Multiple promotion types support
  - Bonus balance allocation for freespins
  - Regular balance allocation for other promos
  - Promotional round creation

## Supported Promotion Types

- `FSW` - Freespin wins
- `JPW` - Jackpot wins
- `CB` - Cashback
- `TW` - Tournament wins
- `RW` - Reward wins
- `REW` - Red envelope wins
- `CDW` - Cash drop wins
- `RB` - Rakeback

## Mock Data Management

### Default Customers

The service initializes with three default customers:

1. **Customer ID**: `2019045569026`
   - Balance: $1000.00
   - Bonus Balance: $50.00
   - Currency: USD
   - Status: Active

2. **Customer ID**: `2020120400005`
   - Balance: €500.00
   - Bonus Balance: €25.00
   - Currency: EUR
   - Status: Active

3. **Customer ID**: `2021030100001`
   - Balance: $0.00
   - Bonus Balance: $0.00
   - Currency: USD
   - Status: Suspended

### Data Storage

The service maintains in-memory storage for:
- Customer data (balances, currencies, status)
- Transaction history
- Round information
- Session data

## Error Handling

The service implements comprehensive error handling with proper Generic Vendor API error codes:

- `TOKEN_INVALID` - Invalid or expired tokens
- `CUSTOMER_NOT_FOUND` - Customer doesn't exist
- `PLAYER_SUSPENDED` - Customer is suspended
- `INSUFFICIENT_FUNDS` - Not enough balance for debit
- `BET_ALREADY_SETTLED` - Duplicate transaction
- `BET_RECORD_NOT_FOUND` - Round doesn't exist
- `TRANSACTION_NOT_FOUND` - Transaction doesn't exist
- `PROMOTION_TYPE_NOT_SUPPORTED` - Invalid promo type

## Usage Examples

### Basic Authentication
```typescript
const authRequest: OperatorAuthRequest = {
    customer: "2019045569026",
    token: "valid_token_here"
};

const response = await mockService.auth(authRequest);
// Returns: { balance: 1000, bonusBalance: 50, code: 0, currency: "USD", status: "SUCCESS", traderId: 1 }
```

### Place a Bet
```typescript
const debitRequest: OperatorDebitRequest = {
    customer: "2019045569026",
    token: "valid_token_here",
    gameId: "slot_game_1",
    amount: 10.00,
    currency: "USD",
    betId: "bet_123",
    trxId: "trx_456"
};

const response = await mockService.debit(debitRequest);
// Deducts $10 from customer balance
```

### Settle a Win
```typescript
const creditRequest: OperatorCreditRequest = {
    customer: "2019045569026",
    token: "valid_token_here",
    gameId: "slot_game_1",
    amount: 25.00,
    currency: "USD",
    betId: "bet_123",
    trxId: "trx_789"
};

const response = await mockService.credit(creditRequest);
// Adds $25 to customer balance
```

## Utility Methods

The service provides several utility methods for testing and data management:

- `getCustomerData(customerId)` - Retrieve customer information
- `getTransactionData(trxId)` - Retrieve transaction details
- `getRoundData(betId)` - Retrieve round information
- `addCustomer(customer)` - Add new customer
- `updateCustomerBalance(customerId, balance, bonusBalance)` - Update balances
- `resetMockData()` - Reset all data to initial state
- `getAllCustomers()` - Get all customers
- `getAllTransactions()` - Get all transactions
- `getAllRounds()` - Get all rounds

## Integration

The MockService is automatically integrated into the mock system through:

1. **MockModule** - Registers the service as a provider
2. **MockController** - Uses the service for all endpoint implementations
3. **Mock Responders** - Can be used alongside for custom behavior

## Testing

The service is designed to work seamlessly with the existing mock infrastructure and can be used for:

- Integration testing
- API development
- Client application testing
- Load testing scenarios

## Configuration

The service uses the existing mock configuration system and integrates with:

- `SettingsService` - For mock settings and amounts
- `TicketService` - For token validation
- Mock interceptors and middleware
